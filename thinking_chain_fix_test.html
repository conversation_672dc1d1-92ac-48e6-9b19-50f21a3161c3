<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>思维链修复测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
            color: #334155;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        h1 {
            color: #1e293b;
            margin-bottom: 30px;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: #fefefe;
        }

        /* 思维链样式 - 参考deepseek.com简洁风格 */
        .thinking-chain {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            margin: 1rem 0;
            overflow: hidden;
            transition: all 0.2s ease;
            position: relative;
        }

        .thinking-chain:hover {
            border-color: #cbd5e1;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .thinking-chain-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 16px;
            cursor: pointer;
            background: #ffffff;
            border-bottom: 1px solid #e2e8f0;
            transition: background-color 0.2s ease;
            user-select: none;
            min-height: 48px;
        }

        .thinking-chain-header:hover {
            background: #f1f5f9;
        }

        .thinking-chain-title {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            font-weight: 500;
            color: #475569;
            flex: 1;
        }

        .thinking-chain-icon {
            width: 16px;
            height: 16px;
            transition: transform 0.2s ease;
            color: #64748b;
            flex-shrink: 0;
        }

        .thinking-chain-header.expanded .thinking-chain-icon {
            transform: rotate(90deg);
            color: #3b82f6;
        }

        .thinking-chain-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background: #ffffff;
        }

        .thinking-chain-content.expanded {
            max-height: 400px;
            overflow-y: auto;
        }

        .thinking-chain-inner {
            padding: 16px;
            color: #374151;
            font-size: 14px;
            line-height: 1.6;
            white-space: pre-wrap;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        .thinking-chain-inner.typing::after {
            content: '|';
            color: #3b82f6;
            animation: blink 1s infinite;
            margin-left: 2px;
        }

        .thinking-chain-content::-webkit-scrollbar {
            width: 4px;
        }

        .thinking-chain-content::-webkit-scrollbar-track {
            background: transparent;
        }

        .thinking-chain-content::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 2px;
        }

        .thinking-chain-content::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        .thinking-chain-badge {
            background: #e0f2fe;
            color: #0369a1;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            white-space: nowrap;
        }

        /* 思维链动画效果 */
        .thinking-chain.thinking {
            border-color: #3b82f6;
            background: #f0f9ff;
        }

        .thinking-chain.thinking .thinking-chain-header {
            background: #eff6ff;
        }

        .thinking-chain.thinking .thinking-chain-badge {
            background: #3b82f6;
            color: #ffffff;
            animation: thinking-pulse 1.5s ease-in-out infinite;
        }

        @keyframes thinking-pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.7;
            }
        }

        @keyframes blink {
            0%, 50% {
                opacity: 1;
            }
            51%, 100% {
                opacity: 0;
            }
        }

        .btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: background 0.2s ease;
        }

        .btn:hover {
            background: #2563eb;
        }

        .message-content {
            background: white;
            padding: 16px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>思维链修复测试</h1>
        
        <div class="test-section">
            <h3>测试1：完成的思维链</h3>
            <div class="message-content">
                <p>这是一个普通的回答内容。</p>
                
                <div class="thinking-chain" data-thinking-id="test-1">
                    <div class="thinking-chain-header" onclick="toggleThinkingChain('test-1')">
                        <div class="thinking-chain-title">
                            <svg class="thinking-chain-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                            <span>思维过程</span>
                        </div>
                        <span class="thinking-chain-badge">展开查看</span>
                    </div>
                    <div class="thinking-chain-content" id="test-1-content">
                        <div class="thinking-chain-inner">这是一个思维链的内容示例。

用户问了一个关于AI的问题，我需要仔细思考如何回答。

首先，我需要分析：
1. 用户的真实需求是什么？
2. 我应该从哪个角度回答？
3. 需要提供什么样的信息？

经过思考，我认为应该这样回答...</div>
                    </div>
                </div>
                
                <p>基于我的分析，我的建议是：[具体回答内容]</p>
            </div>
        </div>

        <div class="test-section">
            <h3>测试2：思考中的思维链</h3>
            <div class="message-content">
                <p>这是另一个回答的开始...</p>
                
                <div class="thinking-chain thinking" data-thinking-id="test-2">
                    <div class="thinking-chain-header">
                        <div class="thinking-chain-title">
                            <svg class="thinking-chain-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                            <span>思维过程</span>
                        </div>
                        <span class="thinking-chain-badge">思考中...</span>
                    </div>
                    <div class="thinking-chain-content expanded" id="test-2-content">
                        <div class="thinking-chain-inner typing" id="test-2-inner">正在思考问题的解决方案...</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>控制按钮</h3>
            <button class="btn" onclick="simulateThinking()">模拟思考过程</button>
            <button class="btn" onclick="completeThinking()">完成思考</button>
            <button class="btn" onclick="resetTest()">重置测试</button>
        </div>
    </div>

    <script>
        // 切换思维链展开/折叠状态
        function toggleThinkingChain(id) {
            const header = document.querySelector(`[data-thinking-id="${id}"] .thinking-chain-header`);
            const content = document.getElementById(`${id}-content`);
            const badge = header.querySelector('.thinking-chain-badge');

            if (!header || !content || !badge) return;

            const isExpanded = header.classList.contains('expanded');

            if (isExpanded) {
                // 折叠
                header.classList.remove('expanded');
                content.classList.remove('expanded');
                badge.textContent = '展开查看';
            } else {
                // 展开
                header.classList.add('expanded');
                content.classList.add('expanded');
                badge.textContent = '收起';
            }
        }

        let thinkingInterval;
        let thinkingText = '';

        function simulateThinking() {
            const thinkingChain = document.querySelector('[data-thinking-id="test-2"]');
            const badge = thinkingChain.querySelector('.thinking-chain-badge');
            const inner = document.getElementById('test-2-inner');
            
            thinkingChain.classList.add('thinking');
            badge.textContent = '思考中...';
            
            const thoughts = [
                '正在分析问题...',
                '考虑不同的解决方案...',
                '评估各种可能性...',
                '寻找最佳答案...',
                '整理思路中...'
            ];
            
            let index = 0;
            thinkingInterval = setInterval(() => {
                if (index < thoughts.length) {
                    thinkingText += thoughts[index] + '\n\n';
                    inner.textContent = thinkingText;
                    index++;
                } else {
                    clearInterval(thinkingInterval);
                }
            }, 1000);
        }

        function completeThinking() {
            clearInterval(thinkingInterval);
            const thinkingChain = document.querySelector('[data-thinking-id="test-2"]');
            const badge = thinkingChain.querySelector('.thinking-chain-badge');
            const content = document.getElementById('test-2-content');
            const header = thinkingChain.querySelector('.thinking-chain-header');
            const inner = document.getElementById('test-2-inner');
            
            thinkingChain.classList.remove('thinking');
            inner.classList.remove('typing');
            badge.textContent = '展开查看';
            
            // 默认折叠完成的思维链
            header.classList.remove('expanded');
            content.classList.remove('expanded');
        }

        function resetTest() {
            clearInterval(thinkingInterval);
            thinkingText = '';
            const inner = document.getElementById('test-2-inner');
            inner.textContent = '正在思考问题的解决方案...';
            inner.classList.add('typing');
            
            const thinkingChain = document.querySelector('[data-thinking-id="test-2"]');
            const badge = thinkingChain.querySelector('.thinking-chain-badge');
            const content = document.getElementById('test-2-content');
            const header = thinkingChain.querySelector('.thinking-chain-header');
            
            thinkingChain.classList.add('thinking');
            badge.textContent = '思考中...';
            header.classList.remove('expanded');
            content.classList.add('expanded');
        }
    </script>
</body>
</html>
